'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import {
  Download,
  RefreshCw,
  Users,
  UserCheck,
  Clock,
  AlertTriangle,
  Search,
  Calendar,
} from 'lucide-react'
import { format } from 'date-fns'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  Pa<PERSON>ationPrevious,
} from '@/components/ui/pagination'

// Import chart components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'

// School Report Interface (Real Data Structure)
interface SchoolReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  entry: boolean
  entryTime: string | null
  lateEntry: boolean
  lateEntryTime: string | null
  excusedAbsence: boolean
  excusedAbsenceTime?: string | null
  temporaryLeave: boolean
  temporaryLeaveTime?: string | null
  returnFromLeave: boolean
  returnFromLeaveTime?: string | null
  sick: boolean
  sickTime?: string | null
}

// School Statistics Interface
interface SchoolStats {
  total: number
  entry: number
  lateEntry: number
  excusedAbsence: number
  temporaryLeave: number
  returnFromLeave: number
  sick: number
  entryPercentage: number
  lateEntryPercentage: number
  excusedAbsencePercentage: number
  temporaryLeavePercentage: number
  returnFromLeavePercentage: number
  sickPercentage: number
}

// Trend Data for Charts
interface SchoolTrendData {
  date: string
  entry: number
  lateEntry: number
  excusedAbsence: number
}

export default function SchoolReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management for real data
  const [reports, setReports] = useState<SchoolReport[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total: 0,
    entry: 0,
    lateEntry: 0,
    excusedAbsence: 0,
    temporaryLeave: 0,
    returnFromLeave: 0,
    sick: 0,
    entryPercentage: 0,
    lateEntryPercentage: 0,
    excusedAbsencePercentage: 0,
    temporaryLeavePercentage: 0,
    returnFromLeavePercentage: 0,
    sickPercentage: 0,
  })
  const [trendData, setTrendData] = useState<SchoolTrendData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [searchQuery, setSearchQuery] = useState('')
  const [isExporting, setIsExporting] = useState(false)
  const [availableClasses, setAvailableClasses] = useState<Array<{ value: string; label: string }>>(
    []
  )
  const [isLoadingClasses, setIsLoadingClasses] = useState(true)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50) // 50 students per page for 3000 students

  // Fetch available classes
  const fetchClasses = async () => {
    try {
      setIsLoadingClasses(true)
      const response = await fetch('/api/classes')
      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }
      const classes = await response.json()

      // Transform classes to dropdown format
      const classOptions = classes.map((cls: any) => ({
        value: cls.name.toLowerCase().replace(/\s+/g, '-'),
        label: cls.name,
      }))

      setAvailableClasses(classOptions)
    } catch (error) {
      console.error('Error fetching classes:', error)
      toast({
        title: 'Gagal memuat daftar kelas',
        description: 'Menggunakan daftar kelas default',
        variant: 'destructive',
      })
    } finally {
      setIsLoadingClasses(false)
    }
  }

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Super Admin, Teacher, and Receptionist can access school reports
      if (
        admin.role !== 'super_admin' &&
        admin.role !== 'teacher' &&
        admin.role !== 'receptionist'
      ) {
        router.push('/admin/home')
        return
      }
      // Fetch classes when admin is authenticated
      fetchClasses()
    }
  }, [admin, sessionLoading, router])

  // Fetch school reports data
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('date', date)
      queryParams.append('reportType', 'school') // Only school data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch school reports')
      }

      const data = await response.json()
      setReports(data)

      // Calculate school statistics based on data type
      const total = data.length
      let entryCount,
        lateEntryCount,
        excusedAbsenceCount,
        temporaryLeaveCount,
        returnFromLeaveCount,
        sickCount

      // Check if this is aggregated data (week, 30 days, longer periods)
      const isAggregatedData = [
        'week',
        '30days',
        'current-month',
        'last-3months',
        'school-year',
      ].includes(date)

      if (isAggregatedData && data.length > 0 && data[0].aggregatedCounts) {
        // For aggregated data, sum up the counts
        entryCount = data.reduce((sum: number, r: any) => sum + (r.aggregatedCounts?.entry || 0), 0)
        lateEntryCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.lateEntry || 0),
          0
        )
        excusedAbsenceCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.excusedAbsence || 0),
          0
        )
        temporaryLeaveCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.temporaryLeave || 0),
          0
        )
        returnFromLeaveCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.returnFromLeave || 0),
          0
        )
        sickCount = data.reduce((sum: number, r: any) => sum + (r.aggregatedCounts?.sick || 0), 0)
      } else {
        // For daily/weekly data, count boolean values
        entryCount = data.filter((r: SchoolReport) => r.entry).length
        lateEntryCount = data.filter((r: SchoolReport) => r.lateEntry).length
        excusedAbsenceCount = data.filter((r: SchoolReport) => r.excusedAbsence).length
        temporaryLeaveCount = data.filter((r: SchoolReport) => r.temporaryLeave).length
        returnFromLeaveCount = data.filter((r: SchoolReport) => r.returnFromLeave).length
        sickCount = data.filter((r: SchoolReport) => r.sick).length
      }

      setStats({
        total,
        entry: entryCount,
        lateEntry: lateEntryCount,
        excusedAbsence: excusedAbsenceCount,
        temporaryLeave: temporaryLeaveCount,
        returnFromLeave: returnFromLeaveCount,
        sick: sickCount,
        entryPercentage: total > 0 ? Math.round((entryCount / total) * 100) : 0,
        lateEntryPercentage: total > 0 ? Math.round((lateEntryCount / total) * 100) : 0,
        excusedAbsencePercentage: total > 0 ? Math.round((excusedAbsenceCount / total) * 100) : 0,
        temporaryLeavePercentage: total > 0 ? Math.round((temporaryLeaveCount / total) * 100) : 0,
        returnFromLeavePercentage: total > 0 ? Math.round((returnFromLeaveCount / total) * 100) : 0,
        sickPercentage: total > 0 ? Math.round((sickCount / total) * 100) : 0,
      })

      // Generate trend data (mock for now, can be enhanced with real historical data)
      const mockTrendData: SchoolTrendData[] = [
        {
          date: '2024-01-01',
          entry: entryCount,
          lateEntry: lateEntryCount,
          excusedAbsence: excusedAbsenceCount,
        },
        {
          date: '2024-01-02',
          entry: Math.max(0, entryCount - 3),
          lateEntry: Math.max(0, lateEntryCount + 1),
          excusedAbsence: Math.max(0, excusedAbsenceCount - 1),
        },
        {
          date: '2024-01-03',
          entry: Math.max(0, entryCount + 1),
          lateEntry: Math.max(0, lateEntryCount - 1),
          excusedAbsence: Math.max(0, excusedAbsenceCount + 2),
        },
        {
          date: '2024-01-04',
          entry: entryCount,
          lateEntry: lateEntryCount,
          excusedAbsence: excusedAbsenceCount,
        },
        {
          date: '2024-01-05',
          entry: Math.max(0, entryCount - 1),
          lateEntry: Math.max(0, lateEntryCount + 2),
          excusedAbsence: Math.max(0, excusedAbsenceCount - 1),
        },
      ]
      setTrendData(mockTrendData)
    } catch (error) {
      console.error('Error fetching school reports:', error)
      toast({
        title: 'Gagal memuat laporan sekolah',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export school reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate school-specific CSV content
      const csvContent = generateSchoolCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-sekolah-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan sekolah berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting school reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Generate school-specific CSV
  const generateSchoolCSV = (reports: SchoolReport[], stats: SchoolStats): string => {
    const reportDate =
      date === 'today' ? 'Hari Ini' : date === 'yesterday' ? 'Kemarin' : 'Minggu Ini'
    const className = classFilter === 'all' ? 'Semua Kelas' : classFilter

    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Masuk',
      'Waktu_Masuk',
      'Terlambat',
      'Waktu_Terlambat',
      'Ijin_Tidak_Hadir',
      'Waktu_Ijin',
      'Ijin_Sementara',
      'Waktu_Ijin_Sementara',
      'Kembali_Ijin',
      'Waktu_Kembali',
      'Sakit',
      'Waktu_Sakit',
      'Skor_Kehadiran',
    ]

    const rows = reports.map((report, index) => {
      const attendanceCount = report.entry ? 1 : 0
      const attendanceScore = report.excusedAbsence || report.sick ? 100 : attendanceCount * 100

      return [
        index + 1,
        report.uniqueCode,
        report.name,
        report.className,
        report.summaryDate,
        report.entry ? 'HADIR' : 'TIDAK_HADIR',
        report.entryTime || '-',
        report.lateEntry ? 'YA' : 'TIDAK',
        report.lateEntryTime || '-',
        report.excusedAbsence ? 'YA' : 'TIDAK',
        report.excusedAbsenceTime || '-',
        report.temporaryLeave ? 'YA' : 'TIDAK',
        report.temporaryLeaveTime || '-',
        report.returnFromLeave ? 'YA' : 'TIDAK',
        report.returnFromLeaveTime || '-',
        report.sick ? 'YA' : 'TIDAK',
        report.sickTime || '-',
        `${attendanceScore}%`,
      ]
    })

    const csvContent = [
      `=== LAPORAN SEKOLAH - ${reportDate} ===`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Masuk: ${stats.entry} (${stats.entryPercentage}%)`,
      `Terlambat: ${stats.lateEntry} (${stats.lateEntryPercentage}%)`,
      `Ijin Tidak Hadir: ${stats.excusedAbsence} (${stats.excusedAbsencePercentage}%)`,
      `Ijin Sementara: ${stats.temporaryLeave} (${stats.temporaryLeavePercentage}%)`,
      `Kembali dari Ijin: ${stats.returnFromLeave} (${stats.returnFromLeavePercentage}%)`,
      `Sakit: ${stats.sick} (${stats.sickPercentage}%)`,
      '',
      headers.join(','),
      ...rows.map(row => row.join(',')),
    ].join('\n')

    return csvContent
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredReports.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedReports = filteredReports.slice(startIndex, endIndex)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [classFilter, date, searchQuery])

  // Load data on component mount and when filters change
  useEffect(() => {
    if (
      admin &&
      (admin.role === 'super_admin' || admin.role === 'teacher' || admin.role === 'receptionist')
    ) {
      fetchReports()
    }
  }, [admin, date, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'teacher' && admin.role !== 'receptionist') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan sekolah.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Sekolah</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran sekolah siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchReports} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Select value={classFilter} onValueChange={setClassFilter}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Pilih Kelas" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua Kelas</SelectItem>
            {isLoadingClasses ? (
              <SelectItem value="loading" disabled>
                Memuat kelas...
              </SelectItem>
            ) : (
              availableClasses.map(cls => (
                <SelectItem key={cls.value} value={cls.value}>
                  {cls.label}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
        <Select value={date} onValueChange={setDate}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Periode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Hari Ini</SelectItem>
            <SelectItem value="yesterday">Kemarin</SelectItem>
            <SelectItem value="week">7 Hari Terakhir</SelectItem>
            <SelectItem value="30days">30 Hari Terakhir</SelectItem>
            <SelectItem value="current-month">Bulan Ini</SelectItem>
            <SelectItem value="last-3months">3 Bulan Terakhir</SelectItem>
            <SelectItem value="school-year">Tahun Ajaran Ini</SelectItem>
          </SelectContent>
        </Select>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Cari siswa..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">{stats.entry}</div>
                <div className="text-sm font-medium text-gray-600">Masuk</div>
                <div className="mt-1 text-xs text-gray-500">{stats.entryPercentage}% kehadiran</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-amber-600">{stats.lateEntry}</div>
                <div className="text-sm font-medium text-gray-600">Terlambat</div>
                <div className="mt-1 text-xs text-gray-500">{stats.lateEntryPercentage}% siswa</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <Clock className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-red-600">
                  {stats.excusedAbsence + stats.sick}
                </div>
                <div className="text-sm font-medium text-gray-600">Ijin & Sakit</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.excusedAbsencePercentage + stats.sickPercentage}% siswa
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* School Attendance Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Tren Kehadiran Sekolah</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="entry"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Masuk"
                />
                <Line
                  type="monotone"
                  dataKey="lateEntry"
                  stroke="#f59e0b"
                  strokeWidth={2}
                  name="Terlambat"
                />
                <Line
                  type="monotone"
                  dataKey="excusedAbsence"
                  stroke="#ef4444"
                  strokeWidth={2}
                  name="Ijin"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Student Data Matrix - Moved to Bottom */}
      <Card>
        <CardHeader>
          <CardTitle>Data Siswa Sekolah ({filteredReports.length} siswa)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No</TableHead>
                  <TableHead>Kode Siswa</TableHead>
                  <TableHead>Nama</TableHead>
                  <TableHead>Kelas</TableHead>
                  <TableHead>Masuk</TableHead>
                  <TableHead>Terlambat</TableHead>
                  <TableHead>Ijin</TableHead>
                  <TableHead>Sakit</TableHead>
                  <TableHead>Skor</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : paginatedReports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="py-8 text-center text-gray-500">
                      Tidak ada data siswa ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedReports.map((report, index) => {
                    const globalIndex = startIndex + index + 1
                    const isAggregatedData = [
                      'week',
                      '30days',
                      'current-month',
                      'last-3months',
                      'school-year',
                    ].includes(date)

                    // Calculate attendance metrics based on data type
                    let attendanceCount,
                      attendanceScore,
                      entryDisplay,
                      lateEntryDisplay,
                      excusedAbsenceDisplay,
                      sickDisplay

                    if (isAggregatedData && (report as any).aggregatedCounts) {
                      const counts = (report as any).aggregatedCounts
                      attendanceCount = counts.entry

                      // Calculate score based on total possible attendance in period
                      let totalPossibleDays = 1 // Default for daily
                      if (date === 'week') totalPossibleDays = 7
                      else if (date === '30days') totalPossibleDays = 30
                      else if (date === 'current-month') totalPossibleDays = 31
                      else if (date === 'last-3months') totalPossibleDays = 90
                      else if (date === 'school-year') totalPossibleDays = 200 // ~200 school days

                      attendanceScore =
                        counts.excusedAbsence > 0 || counts.sick > 0
                          ? 100
                          : Math.round((counts.entry / totalPossibleDays) * 100)

                      // Show total counts for aggregated data
                      entryDisplay = `${counts.entry} total`
                      lateEntryDisplay = counts.lateEntry > 0 ? `${counts.lateEntry} total` : '-'
                      excusedAbsenceDisplay =
                        counts.excusedAbsence > 0 ? `${counts.excusedAbsence} hari` : '-'
                      sickDisplay = counts.sick > 0 ? `${counts.sick} hari` : '-'
                    } else {
                      // Show checkmarks for today/yesterday
                      attendanceCount = report.entry ? 1 : 0
                      attendanceScore =
                        report.excusedAbsence || report.sick ? 100 : attendanceCount * 100

                      entryDisplay = report.entry ? '✓' : '✗'
                      lateEntryDisplay = report.lateEntry ? 'Terlambat' : '-'
                      excusedAbsenceDisplay = report.excusedAbsence ? 'Ijin' : '-'
                      sickDisplay = report.sick ? 'Sakit' : '-'
                    }

                    return (
                      <TableRow key={report.uniqueCode}>
                        <TableCell>{globalIndex}</TableCell>
                        <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                        <TableCell className="font-medium">{report.name}</TableCell>
                        <TableCell>{report.className}</TableCell>
                        <TableCell>
                          <Badge variant={report.entry ? 'default' : 'secondary'}>
                            {entryDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.lateEntry ? 'destructive' : 'secondary'}>
                            {lateEntryDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.excusedAbsence ? 'outline' : 'secondary'}>
                            {excusedAbsenceDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.sick ? 'outline' : 'secondary'}>
                            {sickDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              attendanceScore >= 80
                                ? 'default'
                                : attendanceScore >= 50
                                  ? 'secondary'
                                  : 'destructive'
                            }
                          >
                            {attendanceScore}%
                          </Badge>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Menampilkan {startIndex + 1}-{Math.min(endIndex, filteredReports.length)} dari{' '}
                {filteredReports.length} siswa
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      className={
                        currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      className={
                        currentPage === totalPages
                          ? 'pointer-events-none opacity-50'
                          : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
